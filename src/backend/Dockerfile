FROM node:20-alpine

WORKDIR /app

# Install dos2unix to fix line endings
RUN apk add --no-cache dos2unix

# Copy package files first for better layer caching
COPY package.json package-lock.json ./

# Install dependencies with cache mount for faster builds
RUN --mount=type=cache,target=/root/.npm \
    npm ci --cache /root/.npm --prefer-offline

# Copy rest of source code
COPY . .

# Copy entrypoint script separately and fix it (ensure it has executable permissions)
COPY entrypoint.sh ./
RUN dos2unix ./entrypoint.sh && chmod +x ./entrypoint.sh

# Generate Prisma client
RUN npx prisma generate

# Expose the port
EXPOSE 8080

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]