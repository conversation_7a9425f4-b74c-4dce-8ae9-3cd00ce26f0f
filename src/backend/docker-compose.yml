version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # Use Supabase instead of local PostgreSQL
      - DATABASE_URL=${SUPABASE_DATABASE_URL:-}
      - SUPABASE_URL=${SUPABASE_URL:-}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-}
      - NODE_ENV=development
      - PORT=8080
    volumes:
      - ./:/app
      - /app/node_modules