services:
  pathforge_ai_agent_service:
    build:
      context: .
      dockerfile: docker/Dockerfile.service
      # Enable BuildKit cache mounts for faster builds
      cache_from:
        - pathforge_ai_agent_service:latest
      target: runtime
    ports:
      - "8000:8000"
    environment:
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - LANGSMITH_API_KEY=${LANGSMITH_API_KEY:-}
      - LANGSMITH_PROJECT=${LANGSMITH_PROJECT:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
      # Database Configuration (using in-memory/file-based for integration testing)
      - DATABASE_TYPE=sqlite
      - DATABASE_URL=sqlite:///./pathforge_integration.db
    volumes:
      - pathforge_data:/app/data
      - .:/app/src
    networks:
      - integration
    restart: unless-stopped

  pathforge_ai_backend:
    build:
      context: src/backend
      dockerfile: Dockerfile
      cache_from:
        - pathforge_ai_backend:latest
    ports:
      - "8080:8080"
    environment:
      # Use Supabase PostgreSQL connection with SSL
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=require
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - NODE_ENV=development
      - PORT=8080
    volumes:
      - ./src/backend:/app
      - /app/node_modules
    networks:
      - integration
    restart: unless-stopped

  pathforge_ai_streamlit_app:
    build:
      context: .
      dockerfile: docker/Dockerfile.app
      cache_from:
        - pathforge_ai_streamlit_app:latest
      target: runtime
    ports:
      - "8501:8501"
    environment:
      - AGENT_URL=http://pathforge_ai_agent_service:8000
      - BACKEND_URL=http://pathforge_ai_backend:8080
      # API Keys - needed for settings validation
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-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}
      - OPENROUTER_MODEL=${OPENROUTER_MODEL:-qwen/qwen3-235b-a22b}
      - OPENROUTER_BASEURL=${OPENROUTER_BASEURL:-https://openrouter.ai/api/v1}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT:-}
      - AZURE_OPENAI_DEPLOYMENT_MAP=${AZURE_OPENAI_DEPLOYMENT_MAP:-}
      - USE_FAKE_MODEL=${USE_FAKE_MODEL:-false}
      - USE_AWS_BEDROCK=${USE_AWS_BEDROCK:-false}
      - OLLAMA_MODEL=${OLLAMA_MODEL:-}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-}
      - DEFAULT_MODEL=${DEFAULT_MODEL:-openrouter}
      # Other API keys
      - OPENWEATHERMAP_API_KEY=${OPENWEATHERMAP_API_KEY:-********************************}
      - BRAVE_SEARCH_API_KEY=${BRAVE_SEARCH_API_KEY:-BSAm3V_RwMeOMpifscQLjSfMj5y034x}
      # LangSmith configuration
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2:-false}
      - LANGCHAIN_PROJECT=${LANGCHAIN_PROJECT:-default}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY:-}
    volumes:
      - .:/app/src
    networks:
      - integration
    depends_on:
      - pathforge_ai_agent_service
    restart: unless-stopped

  pathforge_ai_frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
      cache_from:
        - pathforge_ai_frontend:latest
      target: production
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_AGENT_URL=http://localhost:8000
      - REACT_APP_STREAMLIT_URL=http://localhost:8501
    networks:
      - integration
    depends_on:
      - pathforge_ai_backend
    restart: unless-stopped

volumes:
  pathforge_data:
    driver: local

networks:
  integration:
    driver: bridge
