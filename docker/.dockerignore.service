# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# Development files
.env.local
.env.example
.vscode/
.idea/
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Streamlit specific
.streamlit/

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore*

# CI/CD
.github/
.gitlab-ci.yml

# Scripts
scripts/

# Tests
tests/

# Client code (not needed for service)
src/client/
src/streamlit_app.py
src/run_client.py

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Large data files
*.csv
*.json
*.xml
*.parquet
data/
