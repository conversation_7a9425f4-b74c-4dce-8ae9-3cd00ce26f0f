name: PathForge AI - Conditional Docker Build

on:
  workflow_call:
    inputs:
      push:
        description: 'Push images to registry'
        required: false
        type: boolean
        default: false
      registry:
        description: 'Docker registry'
        required: false
        type: string
        default: ''
      tag-prefix:
        description: 'Tag prefix for images'
        required: false
        type: string
        default: 'latest'
      report-sizes:
        description: 'Report Docker image sizes in summary'
        required: false
        type: boolean
        default: false
      force-build-all:
        description: 'Force build all services regardless of changes'
        required: false
        type: boolean
        default: false
    outputs:
      agent-service-built:
        description: 'Whether agent service was built'
        value: ${{ jobs.changes.outputs.agent_service }}
      streamlit-app-built:
        description: 'Whether streamlit app was built'
        value: ${{ jobs.changes.outputs.streamlit_app }}
      frontend-built:
        description: 'Whether frontend was built'
        value: ${{ jobs.changes.outputs.frontend_docker }}
      backend-service-built:
        description: 'Whether backend service was built'
        value: ${{ jobs.changes.outputs.backend_service }}
    secrets:
      HEROKU_API_KEY:
        required: false
      HEROKU_EMAIL:
        required: false
      # Keep backward compatibility for other Docker registries
      DOCKER_USERNAME:
        required: false
      DOCKER_PASSWORD:
        required: false

jobs:
  # Detect changes to determine what to build
  changes:
    uses: ./.github/workflows/reusable-changes.yml
    permissions:
      contents: read
      pull-requests: read # Added this line

  # Debug change detection outputs
  debug-changes:
    needs: changes
    runs-on: ubuntu-latest
    steps:
    - name: Debug change detection
      run: |
        echo "## 🔍 Change Detection Debug" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Output | Value |" >> $GITHUB_STEP_SUMMARY
        echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
        echo "| force_build_all | ${{ inputs.force-build-all }} |" >> $GITHUB_STEP_SUMMARY
        echo "| agent_service | ${{ needs.changes.outputs.agent_service }} |" >> $GITHUB_STEP_SUMMARY
        echo "| streamlit_app | ${{ needs.changes.outputs.streamlit_app }} |" >> $GITHUB_STEP_SUMMARY
        echo "| frontend_docker | ${{ needs.changes.outputs.frontend_docker }} |" >> $GITHUB_STEP_SUMMARY
        echo "| backend_service | ${{ needs.changes.outputs.backend_service }} |" >> $GITHUB_STEP_SUMMARY
        echo "| frontend | ${{ needs.changes.outputs.frontend }} |" >> $GITHUB_STEP_SUMMARY
        echo "| python | ${{ needs.changes.outputs.python }} |" >> $GITHUB_STEP_SUMMARY
        echo "| docker | ${{ needs.changes.outputs.docker }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Condition result:** ${{ inputs.force-build-all == true || needs.changes.outputs.agent_service == 'true' || needs.changes.outputs.streamlit_app == 'true' || needs.changes.outputs.frontend_docker == 'true' || needs.changes.outputs.backend_service == 'true' }}" >> $GITHUB_STEP_SUMMARY

  # Build only the services that have changes
  docker-build:
    needs: changes
    if: inputs.force-build-all == true || needs.changes.outputs.agent_service == 'true' || needs.changes.outputs.streamlit_app == 'true' || needs.changes.outputs.frontend_docker == 'true' || needs.changes.outputs.backend_service == 'true'
    uses: ./.github/workflows/reusable-docker-build.yml
    permissions:
      contents: read
      packages: write
    with:
      push: ${{ inputs.push }}
      registry: ${{ inputs.registry }}
      tag-prefix: ${{ inputs.tag-prefix }}
      report-sizes: ${{ inputs.report-sizes }}
      build-agent-service: ${{ inputs.force-build-all == true || needs.changes.outputs.agent_service == 'true' }}
      build-streamlit-app: ${{ inputs.force-build-all == true || needs.changes.outputs.streamlit_app == 'true' }}
      build-frontend: ${{ inputs.force-build-all == true || needs.changes.outputs.frontend_docker == 'true' }}
      build-backend-service: ${{ inputs.force-build-all == true || needs.changes.outputs.backend_service == 'true' }}
    secrets:
      HEROKU_API_KEY: ${{ secrets.HEROKU_API_KEY }}
      HEROKU_EMAIL: ${{ secrets.HEROKU_EMAIL }}
      DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
