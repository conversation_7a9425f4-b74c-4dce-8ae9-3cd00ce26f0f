version: '3.8'

services:
  pathforge_ai_agent_service:
    extends:
      file: docker-compose.full.yml
      service: pathforge_ai_agent_service
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.agent.rule=Host(`agent.localhost`)"
      - "traefik.http.services.agent.loadbalancer.server.port=8000"

  pathforge_ai_backend:
    extends:
      file: docker-compose.full.yml
      service: pathforge_ai_backend
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.localhost`)"
      - "traefik.http.services.backend.loadbalancer.server.port=8080"

  pathforge_ai_frontend:
    extends:
      file: docker-compose.full.yml
      service: pathforge_ai_frontend
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`app.localhost`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=80"

  pathforge_ai_streamlit_app:
    extends:
      file: docker-compose.full.yml
      service: pathforge_ai_streamlit_app
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.streamlit.rule=Host(`dashboard.localhost`)"
      - "traefik.http.services.streamlit.loadbalancer.server.port=8501"

  # Reverse proxy for production
  traefik:
    image: traefik:v2.10
    command:
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8090:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - app_network
    restart: unless-stopped

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - app_network
    restart: unless-stopped

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - app_network
    restart: unless-stopped

networks:
  app_network:
    driver: bridge

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
